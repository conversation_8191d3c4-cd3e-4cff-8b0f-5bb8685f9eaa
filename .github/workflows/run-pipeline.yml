name: Run Data Pipeline

on:
  schedule:
    - cron: '0 2 * * 0'  # Weekly on Sunday at 2 AM UTC
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to run pipeline in'
        required: true
        default: 'dev'
        type: choice
        options:
          - dev
          - staging
          - prod
      skip_vm_creation:
        description: 'Skip VM creation (use existing VM)'
        required: false
        default: false
        type: boolean
      pipeline_action:
        description: 'Pipeline action to perform'
        required: true
        default: 'run-with-cleanup'
        type: choice
        options:
          - run-with-cleanup
          - run-keep-vm
          - test-ssh-only
          - destroy-existing

env:
  TF_VERSION: '1.6.0'
  TF_WORKING_DIR: './infrastructure/terraform'

jobs:
  debug-inputs:
    name: Debug Workflow Inputs
    runs-on: ubuntu-latest
    steps:
      - name: Display Workflow Inputs
        run: |
          echo "=== Workflow Debug Information ==="
          echo "Triggered by: ${{ github.event_name }}"
          echo "Branch: ${{ github.ref }}"
          echo "Environment: ${{ github.event.inputs.environment || 'dev' }}"
          echo "Skip VM Creation: ${{ github.event.inputs.skip_vm_creation }}"
          echo "Pipeline Action: ${{ github.event.inputs.pipeline_action || 'run-with-cleanup' }}"
          echo ""
          echo "=== Job Execution Conditions ==="
          echo "Is workflow_dispatch: ${{ github.event_name == 'workflow_dispatch' }}"
          echo "Is scheduled: ${{ github.event_name == 'schedule' }}"
          echo "Should run destroy: ${{ github.event.inputs.pipeline_action == 'destroy-existing' }}"
          echo "Should create VM: ${{ !github.event.inputs.skip_vm_creation && github.event.inputs.pipeline_action != 'destroy-existing' }}"
          echo ""
          if [ "${{ github.event_name }}" != "workflow_dispatch" ] && [ "${{ github.event_name }}" != "schedule" ]; then
            echo "⚠️  WARNING: This workflow should only be triggered manually or by schedule!"
            echo "⚠️  Use 'Deploy Data Pipeline Infrastructure' for push-triggered deployments."
            echo "⚠️  No jobs will run because this was triggered by: ${{ github.event_name }}"
          fi
          echo "=== End Debug ==="

  check-job-conditions:
    name: Check Job Execution Conditions
    runs-on: ubuntu-latest
    steps:
      - name: Display Job Conditions
        run: |
          echo "=== Job Execution Conditions ==="
          echo "Event: ${{ github.event_name }}"
          echo "Environment: ${{ github.event.inputs.environment || 'dev' }}"
          echo "Skip VM Creation: ${{ github.event.inputs.skip_vm_creation }}"
          echo "Pipeline Action: ${{ github.event.inputs.pipeline_action || 'run-with-cleanup' }}"
          echo ""
          echo "=== Job Should Run Checks ==="
          echo "Is manual/scheduled: ${{ github.event_name == 'workflow_dispatch' || github.event_name == 'schedule' }}"
          echo "Should destroy: ${{ github.event.inputs.pipeline_action == 'destroy-existing' }}"
          echo "Should create VM: ${{ github.event.inputs.skip_vm_creation != 'true' && github.event.inputs.pipeline_action != 'destroy-existing' }}"
          echo "Should run pipeline: ${{ github.event.inputs.pipeline_action != 'destroy-existing' }}"
          echo ""
          echo "=== Expected Job Execution ==="
          if [ "${{ github.event.inputs.pipeline_action }}" == "destroy-existing" ]; then
            echo "✅ destroy-existing job should run"
            echo "❌ create-vm job should NOT run"
            echo "❌ run-pipeline job should NOT run"
            echo "❌ cleanup-vm job should NOT run"
          elif [ "${{ github.event.inputs.skip_vm_creation }}" == "true" ]; then
            echo "❌ destroy-existing job should NOT run"
            echo "❌ create-vm job should NOT run (using existing VM)"
            echo "✅ run-pipeline job should run"
            echo "❌ cleanup-vm job should NOT run (VM not created)"
          else
            echo "❌ destroy-existing job should NOT run"
            echo "✅ create-vm job should run"
            echo "✅ run-pipeline job should run"
            echo "✅ cleanup-vm job should run (if cleanup action)"
          fi

  destroy-existing:
    name: Destroy Existing Resources
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment || 'dev' }}
    if: |
      (github.event_name == 'workflow_dispatch' || github.event_name == 'schedule') &&
      github.event.inputs.pipeline_action == 'destroy-existing'

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TF_VERSION }}
          terraform_wrapper: false

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Destroy Infrastructure
        working-directory: ${{ env.TF_WORKING_DIR }}
        run: |
          # Create terraform.tfvars for destroy
          cat > terraform.tfvars << EOF
          project_id = "${{ secrets.GCP_PROJECT_ID }}"
          project_name = "data-pipeline"
          region = "${{ secrets.GCP_REGION }}"
          zone = "${{ secrets.GCP_ZONE }}"
          machine_type = "${{ secrets.VM_MACHINE_TYPE || 'e2-standard-4' }}"
          aws_private_key = "${{ secrets.AWS_PRIVATE_KEY }}"
          aws_public_key = "${{ secrets.AWS_PUBLIC_KEY }}"
          aws_hostname = "${{ secrets.AWS_HOSTNAME }}"
          aws_user = "${{ secrets.AWS_USER }}"
          github_repo = "${{ github.server_url }}/${{ github.repository }}"
          github_token = "${{ secrets.GITHUB_TOKEN }}"
          environment = "${{ github.event.inputs.environment || 'dev' }}"
          EOF

          # Update backend configuration
          BUCKET_NAME="${{ secrets.GCP_PROJECT_ID }}-terraform-state-${{ github.event.inputs.environment || 'dev' }}"
          sed -i "s/your-terraform-state-bucket/$BUCKET_NAME/g" main.tf

          # Initialize and destroy
          terraform init \
            -backend-config="bucket=${{ secrets.GCP_PROJECT_ID }}-terraform-state-${{ github.event.inputs.environment || 'dev' }}" \
            -backend-config="prefix=terraform/state/${{ github.event.inputs.environment || 'dev' }}"

          echo "=== Destroying all resources ==="
          terraform destroy -auto-approve

          echo "=== Cleanup completed ==="
  create-vm:
    name: Create Pipeline VM
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment || 'dev' }}
    if: |
      (github.event_name == 'workflow_dispatch' || github.event_name == 'schedule') &&
      github.event.inputs.skip_vm_creation != 'true' &&
      github.event.inputs.pipeline_action != 'destroy-existing'
    
    outputs:
      vm_name: ${{ steps.get-vm-info.outputs.vm_name }}
      vm_zone: ${{ steps.get-vm-info.outputs.vm_zone }}
      
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TF_VERSION }}
          terraform_wrapper: false

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Create terraform.tfvars
        working-directory: ${{ env.TF_WORKING_DIR }}
        run: |
          cat > terraform.tfvars << EOF
          project_id = "${{ secrets.GCP_PROJECT_ID }}"
          project_name = "data-pipeline"
          region = "${{ secrets.GCP_REGION }}"
          zone = "${{ secrets.GCP_ZONE }}"
          machine_type = "${{ secrets.VM_MACHINE_TYPE || 'e2-standard-4' }}"
          aws_private_key = "${{ secrets.AWS_PRIVATE_KEY }}"
          aws_public_key = "${{ secrets.AWS_PUBLIC_KEY }}"
          aws_hostname = "${{ secrets.AWS_HOSTNAME }}"
          aws_user = "${{ secrets.AWS_USER }}"
          github_repo = "${{ github.server_url }}/${{ github.repository }}"
          github_token = "${{ secrets.GITHUB_TOKEN }}"
          environment = "${{ github.event.inputs.environment || 'dev' }}"
          auto_delete_vm = true
          EOF

      - name: Update Terraform Backend Configuration
        working-directory: ${{ env.TF_WORKING_DIR }}
        run: |
          BUCKET_NAME="${{ secrets.GCP_PROJECT_ID }}-terraform-state-${{ github.event.inputs.environment || 'dev' }}"
          sed -i "s/your-terraform-state-bucket/$BUCKET_NAME/g" main.tf

      - name: Terraform Init
        working-directory: ${{ env.TF_WORKING_DIR }}
        run: |
          terraform init \
            -backend-config="bucket=${{ secrets.GCP_PROJECT_ID }}-terraform-state-${{ github.event.inputs.environment || 'dev' }}" \
            -backend-config="prefix=terraform/state/${{ github.event.inputs.environment || 'dev' }}"

      - name: Terraform Apply
        working-directory: ${{ env.TF_WORKING_DIR }}
        run: terraform apply -auto-approve

      - name: Get VM Information
        id: get-vm-info
        working-directory: ${{ env.TF_WORKING_DIR }}
        run: |
          VM_NAME=$(terraform output -raw vm_name)
          VM_ZONE=$(terraform output -raw vm_zone)
          
          echo "vm_name=$VM_NAME" >> $GITHUB_OUTPUT
          echo "vm_zone=$VM_ZONE" >> $GITHUB_OUTPUT
          
          echo "VM Name: $VM_NAME"
          echo "VM Zone: $VM_ZONE"

      - name: Wait for VM Startup
        run: |
          VM_NAME="${{ steps.get-vm-info.outputs.vm_name }}"
          VM_ZONE="${{ steps.get-vm-info.outputs.vm_zone }}"
          PROJECT_ID="${{ secrets.GCP_PROJECT_ID }}"

          echo "Waiting for VM startup to complete..."
          echo "VM Name: $VM_NAME"
          echo "VM Zone: $VM_ZONE"
          echo "Project ID: $PROJECT_ID"

          # Wait up to 20 minutes for startup script to complete (increased from 15)
          MAX_ATTEMPTS=120
          SLEEP_INTERVAL=10

          for i in $(seq 1 $MAX_ATTEMPTS); do
            echo "Checking startup progress... (attempt $i/$MAX_ATTEMPTS) - $(date)"

            # Get serial port output and check for completion
            SERIAL_OUTPUT=$(gcloud compute instances get-serial-port-output $VM_NAME --zone=$VM_ZONE --project=$PROJECT_ID 2>/dev/null || echo "")

            # Check if startup script completed successfully
            if echo "$SERIAL_OUTPUT" | grep -q "Data Pipeline VM Startup Script Completed Successfully"; then
              echo "✅ VM startup completed successfully!"
              echo "=== Final startup summary ==="
              echo "$SERIAL_OUTPUT" | tail -20
              break
            fi

            # Check for failure indicators
            if echo "$SERIAL_OUTPUT" | grep -q "Data Pipeline VM Startup Script FAILED"; then
              echo "❌ VM startup script failed!"
              echo "=== Error logs ==="
              echo "$SERIAL_OUTPUT" | tail -50
              exit 1
            fi

            # Show progress indicators and partial logs
            if [ $((i % 3)) -eq 0 ]; then
              echo "=== Progress indicators (attempt $i) ==="
              # Look for specific progress messages
              if echo "$SERIAL_OUTPUT" | grep -q "Installing essential packages"; then
                echo "📦 Installing packages..."
              elif echo "$SERIAL_OUTPUT" | grep -q "Setting up database client"; then
                echo "🗄️ Setting up database..."
              elif echo "$SERIAL_OUTPUT" | grep -q "Setting up SSH configuration"; then
                echo "🔐 Configuring SSH..."
              elif echo "$SERIAL_OUTPUT" | grep -q "Testing SSH connection"; then
                echo "🔗 Testing connections..."
              elif echo "$SERIAL_OUTPUT" | grep -q "Cloning GitHub repository"; then
                echo "📥 Cloning repository..."
              elif echo "$SERIAL_OUTPUT" | grep -q "Installing Python dependencies"; then
                echo "🐍 Installing Python dependencies..."
              else
                echo "⏳ VM is starting up..."
              fi
            fi

            # Show detailed logs every 10 attempts
            if [ $((i % 10)) -eq 0 ]; then
              echo "=== Detailed startup logs (attempt $i) ==="
              echo "$SERIAL_OUTPUT" | tail -15 || echo "Could not get logs"
            fi

            # Check for timeout
            if [ $i -eq $MAX_ATTEMPTS ]; then
              echo "❌ VM startup timeout after 20 minutes"
              echo "=== Full startup logs ==="
              echo "$SERIAL_OUTPUT" || echo "Could not get final logs"

              # Try to get more diagnostic information
              echo "=== VM Instance Status ==="
              gcloud compute instances describe $VM_NAME --zone=$VM_ZONE --project=$PROJECT_ID --format="value(status)" || echo "Could not get VM status"

              echo "=== Recent VM Operations ==="
              gcloud logging read "resource.type=gce_instance AND resource.labels.instance_id=$VM_NAME" --limit=10 --format="value(timestamp,severity,textPayload)" || echo "Could not get operation logs"

              exit 1
            fi

            sleep $SLEEP_INTERVAL
          done

      - name: Display VM Startup Logs
        run: |
          VM_NAME="${{ steps.get-vm-info.outputs.vm_name }}"
          VM_ZONE="${{ steps.get-vm-info.outputs.vm_zone }}"
          PROJECT_ID="${{ secrets.GCP_PROJECT_ID }}"
          
          echo "=== VM Startup Logs ==="
          gcloud compute instances get-serial-port-output $VM_NAME --zone=$VM_ZONE --project=$PROJECT_ID || echo "Could not retrieve startup logs"

  run-pipeline:
    name: Execute Data Pipeline
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment || 'dev' }}
    needs: [create-vm]
    if: |
      (github.event_name == 'workflow_dispatch' || github.event_name == 'schedule') &&
      always() &&
      (needs.create-vm.result == 'success' || github.event.inputs.skip_vm_creation == 'true') &&
      github.event.inputs.pipeline_action != 'destroy-existing'
    
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Get VM Information
        id: vm-info
        run: |
          echo "=== VM Information Debug ==="
          echo "Skip VM Creation: ${{ github.event.inputs.skip_vm_creation }}"
          echo "Create VM Job Result: ${{ needs.create-vm.result }}"
          echo "Create VM Outputs Available: ${{ needs.create-vm.outputs.vm_name != '' }}"

          if [ "${{ github.event.inputs.skip_vm_creation }}" == "true" ]; then
            # Use existing VM
            VM_NAME="data-pipeline-${{ github.event.inputs.environment || 'dev' }}-pipeline-vm"
            VM_ZONE="${{ secrets.GCP_ZONE }}"
            echo "Using existing VM: $VM_NAME in zone $VM_ZONE"
          else
            # Use newly created VM
            VM_NAME="${{ needs.create-vm.outputs.vm_name }}"
            VM_ZONE="${{ needs.create-vm.outputs.vm_zone }}"
            echo "Using newly created VM: $VM_NAME in zone $VM_ZONE"

            # Validate VM information
            if [ -z "$VM_NAME" ] || [ -z "$VM_ZONE" ]; then
              echo "ERROR: VM information is missing!"
              echo "VM_NAME: '$VM_NAME'"
              echo "VM_ZONE: '$VM_ZONE'"
              echo "Create VM job result: ${{ needs.create-vm.result }}"
              exit 1
            fi
          fi

          echo "vm_name=$VM_NAME" >> $GITHUB_OUTPUT
          echo "vm_zone=$VM_ZONE" >> $GITHUB_OUTPUT
          echo "Final VM: $VM_NAME in $VM_ZONE"

      - name: Execute Pipeline on VM
        run: |
          VM_NAME="${{ steps.vm-info.outputs.vm_name }}"
          VM_ZONE="${{ steps.vm-info.outputs.vm_zone }}"
          PROJECT_ID="${{ secrets.GCP_PROJECT_ID }}"

          echo "Executing data pipeline on VM: $VM_NAME"

          # Create a script file to avoid complex quoting issues
          cat > /tmp/pipeline_script.sh << 'EOF'
          #!/bin/bash
          set -e
          echo 'Starting data pipeline execution...'

          # Test SSH connection as pipeline user
          sudo -u pipeline bash -c '
            cd /home/<USER>

            echo "Testing SSH connection to AWS EC2..."
            if ssh trips "echo \"SSH connection verified: \$(date)\""; then
              echo "SSH connection successful!"
            else
              echo "SSH connection failed, attempting to restart ssh-agent..."
              eval "\$(ssh-agent -s)"
              ssh-add ~/.ssh/aws_private_key
              ssh trips "echo \"SSH connection verified after restart: \$(date)\""
            fi

            # If pipeline repository exists, run the pipeline
            if [ -d "/home/<USER>/pipeline-repo" ]; then
              cd /home/<USER>/pipeline-repo

              # Activate virtual environment if it exists
              if [ -f "venv/bin/activate" ]; then
                source venv/bin/activate
              fi

              # Run pipeline script if it exists
              if [ -f "run_pipeline.py" ]; then
                echo "Running Python pipeline..."
                python run_pipeline.py
              elif [ -f "run_pipeline.sh" ]; then
                echo "Running shell pipeline..."
                bash run_pipeline.sh
              else
                echo "No pipeline script found, running basic data extraction test..."

                # Basic test: dump a sample table from AWS EC2
                ssh trips "mysqldump --single-transaction --routines --triggers --all-databases" > /tmp/aws_db_dump.sql

                # Import to local MariaDB
                mysql -u pipeline -ppipeline123 pipeline_data < /tmp/aws_db_dump.sql

                # Basic verification
                mysql -u pipeline -ppipeline123 -e "SHOW DATABASES;"

                echo "Basic pipeline test completed successfully"
              fi
            else
              echo "Pipeline repository not found, running basic SSH test only"
            fi

            echo "Pipeline execution completed at: \$(date)"
          '
          EOF

          # Validate VM information before executing
          if [ -z "$VM_NAME" ] || [ -z "$VM_ZONE" ]; then
            echo "ERROR: Cannot execute pipeline - VM information is missing!"
            echo "VM_NAME: '$VM_NAME'"
            echo "VM_ZONE: '$VM_ZONE'"
            echo "This usually means the create-vm job failed or was skipped incorrectly."
            exit 1
          fi

          echo "Transferring script to VM: $VM_NAME"
          # Execute the script on the VM
          gcloud compute scp /tmp/pipeline_script.sh $VM_NAME:/tmp/pipeline_script.sh \
            --zone=$VM_ZONE \
            --project=$PROJECT_ID

          echo "Executing pipeline script on VM"
          gcloud compute ssh $VM_NAME \
            --zone=$VM_ZONE \
            --project=$PROJECT_ID \
            --command="chmod +x /tmp/pipeline_script.sh && /tmp/pipeline_script.sh" \
            || echo "Pipeline execution failed"

      - name: Collect Pipeline Logs
        run: |
          VM_NAME="${{ steps.vm-info.outputs.vm_name }}"
          VM_ZONE="${{ steps.vm-info.outputs.vm_zone }}"
          PROJECT_ID="${{ secrets.GCP_PROJECT_ID }}"
          
          echo "=== Collecting Pipeline Execution Logs ==="
          
          # Get the latest logs from the VM
          gcloud compute ssh $VM_NAME \
            --zone=$VM_ZONE \
            --project=$PROJECT_ID \
            --command="
              echo '=== Pipeline Startup Log ==='
              sudo cat /var/log/pipeline-startup.log 2>/dev/null || echo 'Startup log not found'
              
              echo '=== SSH Test Results ==='
              sudo -u pipeline cat /home/<USER>/ssh_test_output.log 2>/dev/null || echo 'SSH test log not found'
              
              echo '=== VM Setup Summary ==='
              sudo -u pipeline cat /home/<USER>/vm_setup_summary.txt 2>/dev/null || echo 'Setup summary not found'
            " || echo "Could not collect logs from VM"

  cleanup-vm:
    name: Cleanup Pipeline VM
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment || 'dev' }}
    needs: [create-vm, run-pipeline]
    if: |
      (github.event_name == 'workflow_dispatch' || github.event_name == 'schedule') &&
      always() &&
      needs.create-vm.result == 'success' &&
      github.event.inputs.skip_vm_creation != 'true' &&
      (github.event.inputs.pipeline_action == 'run-with-cleanup' ||
       github.event.inputs.pipeline_action == 'test-ssh-only' ||
       !github.event.inputs.pipeline_action)
    
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TF_VERSION }}
          terraform_wrapper: false

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Destroy Infrastructure
        working-directory: ${{ env.TF_WORKING_DIR }}
        run: |
          # Create terraform.tfvars for destroy
          cat > terraform.tfvars << EOF
          project_id = "${{ secrets.GCP_PROJECT_ID }}"
          project_name = "data-pipeline"
          region = "${{ secrets.GCP_REGION }}"
          zone = "${{ secrets.GCP_ZONE }}"
          machine_type = "${{ secrets.VM_MACHINE_TYPE || 'e2-standard-4' }}"
          aws_private_key = "${{ secrets.AWS_PRIVATE_KEY }}"
          aws_public_key = "${{ secrets.AWS_PUBLIC_KEY }}"
          aws_hostname = "${{ secrets.AWS_HOSTNAME }}"
          aws_user = "${{ secrets.AWS_USER }}"
          github_repo = "${{ github.server_url }}/${{ github.repository }}"
          github_token = "${{ secrets.GITHUB_TOKEN }}"
          environment = "${{ github.event.inputs.environment || 'dev' }}"
          EOF
          
          # Update backend configuration
          BUCKET_NAME="${{ secrets.GCP_PROJECT_ID }}-terraform-state-${{ github.event.inputs.environment || 'dev' }}"
          sed -i "s/your-terraform-state-bucket/$BUCKET_NAME/g" main.tf

          # Initialize and destroy
          terraform init \
            -backend-config="bucket=${{ secrets.GCP_PROJECT_ID }}-terraform-state-${{ github.event.inputs.environment || 'dev' }}" \
            -backend-config="prefix=terraform/state/${{ github.event.inputs.environment || 'dev' }}"
          
          terraform destroy -auto-approve
