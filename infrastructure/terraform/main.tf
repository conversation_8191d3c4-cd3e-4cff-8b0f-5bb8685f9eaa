terraform {
  required_version = ">= 1.0"
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
  }
  backend "gcs" {
    bucket = "your-terraform-state-bucket"
    prefix = "terraform/state"
  }
}

provider "google" {
  project = var.project_id
  region  = var.region
  zone    = var.zone
}

locals {
  vm_name = "${var.project_name}-${var.environment}-pipeline-vm"

  startup_script = templatefile("${path.module}/startup-script.sh", {
    private_key_content = var.aws_private_key
    public_key_content  = var.aws_public_key
    aws_hostname        = var.aws_hostname
    aws_user            = var.aws_user
    github_repo         = var.github_repo
    github_token        = var.github_token
  })
}

resource "google_compute_network" "pipeline_network" {
  name                    = "${var.project_name}-${var.environment}-network"
  auto_create_subnetworks = false
  description             = "Network for data pipeline VM"
}

resource "google_compute_subnetwork" "pipeline_subnet" {
  name          = "${var.project_name}-${var.environment}-subnet"
  ip_cidr_range = "********/24"
  region        = var.region
  network       = google_compute_network.pipeline_network.id
  description   = "Subnet for data pipeline VM"
}

resource "google_compute_firewall" "allow_ssh" {
  name    = "${var.project_name}-${var.environment}-allow-ssh"
  network = google_compute_network.pipeline_network.name

  allow {
    protocol = "tcp"
    ports    = ["22"]
  }

  source_ranges = ["0.0.0.0/0"]
  target_tags   = ["pipeline-vm"]
  description   = "Allow SSH access to pipeline VM"
}

resource "google_compute_firewall" "allow_internal" {
  name    = "${var.project_name}-${var.environment}-allow-internal"
  network = google_compute_network.pipeline_network.name

  allow {
    protocol = "tcp"
    ports    = ["0-65535"]
  }

  allow {
    protocol = "udp"
    ports    = ["0-65535"]
  }

  allow {
    protocol = "icmp"
  }

  source_ranges = ["********/24"]
  target_tags   = ["pipeline-vm"]
  description   = "Allow internal communication"
}

# Use existing service account by email (no data source needed)
# This avoids needing iam.serviceAccounts.get permission
locals {
  existing_service_account_email = "vm-cuba-buddy-data-ingestion@${var.project_id}.iam.gserviceaccount.com"
}

# Note: IAM permissions for the service account are managed manually in GCP Console
# The service account vm-cuba-buddy-data-ingestion already has the following roles assigned:
# - Cloud Run developer
# - Cloud Scheduler Admin
# - Compute Admin
# - Compute Storage Admin
# - Logging Admin
# - Monitoring Admin
# - Storage Admin
#
# We don't manage these through Terraform to avoid IAM permission issues
# with the GitHub Actions service account

resource "google_compute_instance" "pipeline_vm" {
  name         = local.vm_name
  machine_type = var.machine_type
  zone         = var.zone
  tags         = ["pipeline-vm"]

  boot_disk {
    initialize_params {
      image = var.vm_image
      size  = var.disk_size
      type  = "pd-ssd"  # Use SSD for faster boot and I/O
    }
  }

  network_interface {
    network    = google_compute_network.pipeline_network.id
    subnetwork = google_compute_subnetwork.pipeline_subnet.id

    access_config {
      # Ephemeral public IP
    }
  }

  service_account {
    email = local.existing_service_account_email
    scopes = [
      "https://www.googleapis.com/auth/cloud-platform",
      "https://www.googleapis.com/auth/logging.write",
      "https://www.googleapis.com/auth/monitoring.write"
    ]
  }

  metadata = {
    enable-oslogin = "FALSE"
    startup-script = local.startup_script
    # Optimize boot performance
    serial-port-enable = "TRUE"
    # Enable nested virtualization for better performance
    enable-nested-virtualization = "FALSE"
  }

  # Optimize VM scheduling
  scheduling {
    automatic_restart   = true
    on_host_maintenance = "MIGRATE"
    preemptible         = false
  }

  metadata_startup_script = local.startup_script

  lifecycle {
    create_before_destroy = true
  }

  depends_on = [
    google_compute_network.pipeline_network,
    google_compute_subnetwork.pipeline_subnet
  ]
}

resource "google_storage_bucket" "pipeline_data" {
  name          = "${var.project_id}-pipeline-data-${random_id.bucket_suffix.hex}"
  location      = var.region
  force_destroy = true

  uniform_bucket_level_access = true

  versioning {
    enabled = true
  }

  lifecycle_rule {
    condition {
      age = 30
    }
    action {
      type = "Delete"
    }
  }
}

resource "random_id" "bucket_suffix" {
  byte_length = 4
}

# Note: Storage bucket permissions are handled through the Storage Admin role
# assigned to the service account at the project level, so no bucket-specific
# IAM binding is needed
