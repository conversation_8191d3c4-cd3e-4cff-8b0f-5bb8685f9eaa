#!/bin/bash

# Data Pipeline VM Startup Script
# This script configures SSH keys, installs dependencies, and tests AWS EC2 connectivity

set -e

# Function to log messages to both file and serial console
log_message() {
    local message="$1"
    echo "$message"
    echo "$message" | tee -a /dev/console
    logger -t "pipeline-startup" "$message"
}

# Function to handle errors
handle_error() {
    local line_number=$1
    local error_code=$2
    log_message "ERROR: Script failed at line $line_number with exit code $error_code"
    log_message "=== Data Pipeline VM Startup Script FAILED at $(date) ==="
    exit $error_code
}

# Function to retry commands with exponential backoff
retry_command() {
    local max_attempts=$1
    local delay=$2
    local command="${@:3}"
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        log_message "Attempting: $command (attempt $attempt/$max_attempts)"
        if eval "$command"; then
            log_message "Command succeeded on attempt $attempt"
            return 0
        else
            if [ $attempt -eq $max_attempts ]; then
                log_message "Command failed after $max_attempts attempts"
                return 1
            fi
            log_message "Command failed, retrying in ${delay}s..."
            sleep $delay
            delay=$((delay * 2))  # Exponential backoff
            attempt=$((attempt + 1))
        fi
    done
}

# Set up error handling
trap 'handle_error ${LINENO} $?' ERR

# Logging setup
LOG_FILE="/var/log/pipeline-startup.log"
exec 1> >(tee -a "$LOG_FILE")
exec 2> >(tee -a "$LOG_FILE" >&2)

log_message "=== Data Pipeline VM Startup Script Started at $(date) ==="

# Optimize system for faster startup
log_message "Optimizing system for faster startup..."
export DEBIAN_FRONTEND=noninteractive
# Disable unnecessary services to speed up boot
systemctl disable snapd.service snapd.socket snapd.seeded.service || true
systemctl stop snapd.service snapd.socket snapd.seeded.service || true

# Update system packages with retry logic
log_message "Updating system packages..."
retry_command 3 5 "apt-get update -y -qq"
log_message "System packages updated successfully"

# Install required packages in parallel for faster installation
log_message "Installing essential packages..."
retry_command 3 10 "apt-get install -y -qq python3 python3-pip python3-venv git curl wget unzip openssh-client jq htop vim nano"

log_message "All required packages installed successfully"

# Handle MySQL/MariaDB installation separately due to potential conflicts
log_message "Setting up database client..."
# First try to install mysql-client with retry logic
if retry_command 3 5 "apt-get install -y -qq mysql-client"; then
    log_message "MySQL client installed successfully"
    DB_CLIENT="mysql"
else
    log_message "MySQL client installation failed, trying alternative approach..."
    # If MySQL client fails, just use the default mysql command if available
    if command -v mysql &> /dev/null; then
        log_message "MySQL command already available"
        DB_CLIENT="mysql"
    else
        log_message "No MySQL client available, will skip database operations"
        DB_CLIENT="none"
    fi
fi

# Install Google Cloud SDK (if not already installed)
if ! command -v gcloud &> /dev/null; then
    log_message "Installing Google Cloud SDK..."
    curl -s https://sdk.cloud.google.com | bash
    source /root/.bashrc
    log_message "Google Cloud SDK installed successfully"
else
    log_message "Google Cloud SDK already available"
fi

# Create pipeline user
log_message "Creating pipeline user..."
useradd -m -s /bin/bash pipeline || log_message "User pipeline already exists"
usermod -aG sudo pipeline
log_message "Pipeline user created and configured successfully"

# Setup database (if available)
if [ "$DB_CLIENT" != "none" ]; then
    log_message "Setting up database..."

    # Check if MySQL server is running or can be started
    if systemctl is-active --quiet mysql || systemctl start mysql 2>/dev/null; then
        log_message "MySQL server is running"

        # Try to secure MySQL installation (automated)
        log_message "Configuring MySQL..."
        mysql -e "ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY 'pipeline123';" 2>/dev/null || \
        mysql -e "UPDATE mysql.user SET authentication_string = PASSWORD('pipeline123') WHERE User = 'root';" 2>/dev/null || \
        log_message "MySQL root password setup skipped (may already be configured)"

        mysql -e "DELETE FROM mysql.user WHERE User='';" 2>/dev/null || true
        mysql -e "DELETE FROM mysql.user WHERE User='root' AND Host NOT IN ('localhost', '127.0.0.1', '::1');" 2>/dev/null || true
        mysql -e "DROP DATABASE IF EXISTS test;" 2>/dev/null || true
        mysql -e "DELETE FROM mysql.db WHERE Db='test' OR Db='test\\_%';" 2>/dev/null || true
        mysql -e "FLUSH PRIVILEGES;" 2>/dev/null || true

        # Create pipeline database and user
        log_message "Creating pipeline database..."
        mysql -u root -ppipeline123 -e "CREATE DATABASE IF NOT EXISTS pipeline_data;" 2>/dev/null || \
        mysql -u root -e "CREATE DATABASE IF NOT EXISTS pipeline_data;" 2>/dev/null || \
        log_message "Database creation skipped"

        mysql -u root -ppipeline123 -e "CREATE USER IF NOT EXISTS 'pipeline'@'localhost' IDENTIFIED BY 'pipeline123';" 2>/dev/null || \
        mysql -u root -e "CREATE USER IF NOT EXISTS 'pipeline'@'localhost' IDENTIFIED BY 'pipeline123';" 2>/dev/null || \
        log_message "User creation skipped"

        mysql -u root -ppipeline123 -e "GRANT ALL PRIVILEGES ON pipeline_data.* TO 'pipeline'@'localhost';" 2>/dev/null || \
        mysql -u root -e "GRANT ALL PRIVILEGES ON pipeline_data.* TO 'pipeline'@'localhost';" 2>/dev/null || \
        log_message "Privileges grant skipped"

        mysql -e "FLUSH PRIVILEGES;" 2>/dev/null || true
        log_message "Database setup completed"
    else
        log_message "No MySQL server available, skipping database setup"
    fi
else
    log_message "No database client available, skipping database setup"
fi

# Switch to pipeline user for SSH setup
log_message "Setting up SSH configuration for pipeline user..."
sudo -u pipeline bash << 'EOF'
set -e

# Function to log messages from within the pipeline user context
log_pipeline_message() {
    local message="$1"
    echo "$message"
    echo "$message" | tee -a /dev/console
    logger -t "pipeline-ssh-setup" "$message"
}

# Create SSH directory
log_pipeline_message "Creating SSH directory..."
mkdir -p /home/<USER>/.ssh
chmod 700 /home/<USER>/.ssh

# Create private key file
log_pipeline_message "Setting up SSH private key..."
cat > /home/<USER>/.ssh/aws_private_key << 'PRIVATE_KEY_EOF'
${private_key_content}
PRIVATE_KEY_EOF

# Create public key file
log_pipeline_message "Setting up SSH public key..."
cat > /home/<USER>/.ssh/aws_public_key << 'PUBLIC_KEY_EOF'
${public_key_content}
PUBLIC_KEY_EOF

# Set proper permissions for SSH keys
chmod 600 /home/<USER>/.ssh/aws_private_key
chmod 644 /home/<USER>/.ssh/aws_public_key
log_pipeline_message "SSH key permissions set correctly"

# Create SSH config
log_pipeline_message "Creating SSH configuration..."
cat > /home/<USER>/.ssh/config << 'SSH_CONFIG_EOF'
Host trips
    HostName ${aws_hostname}
    User ${aws_user}
    IdentityFile ~/.ssh/aws_private_key
    StrictHostKeyChecking no
    UserKnownHostsFile /dev/null
    ServerAliveInterval 60
    ServerAliveCountMax 3
    ConnectTimeout 30
SSH_CONFIG_EOF

chmod 600 /home/<USER>/.ssh/config
log_pipeline_message "SSH configuration created successfully"

# Start SSH agent and add key
log_pipeline_message "Starting SSH agent and adding private key..."
eval "$(ssh-agent -s)"

# Check if ssh-agent is running
if ssh-add -l &>/dev/null; then
    log_pipeline_message "SSH agent is running"
else
    log_pipeline_message "Starting SSH agent..."
    eval "$(ssh-agent -s)"
fi

# Add private key to ssh-agent
log_pipeline_message "Adding private key to SSH agent..."
ssh-add /home/<USER>/.ssh/aws_private_key

# List keys in ssh-agent
log_pipeline_message "Keys in SSH agent:"
ssh-add -l

# Test SSH connection to AWS EC2
log_pipeline_message "Testing SSH connection to AWS EC2..."
SSH_OUTPUT_FILE="/home/<USER>/ssh_test_output.log"

# Test the SSH connection and capture output
echo "=== SSH Connection Test Started at $(date) ===" > "$SSH_OUTPUT_FILE"

if timeout 30 ssh -o ConnectTimeout=10 -o BatchMode=yes trips "echo 'SSH connection successful'; hostname; whoami; date" >> "$SSH_OUTPUT_FILE" 2>&1; then
    log_pipeline_message "SSH connection to AWS EC2 successful!"
    echo "SSH connection to AWS EC2 successful!" >> "$SSH_OUTPUT_FILE"

    # Run additional commands to gather system info
    echo "=== Gathering AWS EC2 System Information ===" >> "$SSH_OUTPUT_FILE"
    ssh trips "
        echo 'System Information:';
        uname -a;
        echo 'Disk Usage:';
        df -h;
        echo 'Memory Usage:';
        free -h;
        echo 'MySQL/MariaDB Status:';
        systemctl status mysql || systemctl status mariadb || echo 'MySQL/MariaDB not running';
        echo 'Available Databases:';
        mysql -e 'SHOW DATABASES;' 2>/dev/null || echo 'Cannot connect to MySQL/MariaDB';
    " >> "$SSH_OUTPUT_FILE" 2>&1

    log_pipeline_message "AWS EC2 system information gathered successfully"
else
    log_pipeline_message "SSH connection to AWS EC2 failed!"
    echo "SSH connection to AWS EC2 failed!" >> "$SSH_OUTPUT_FILE"
    echo "Connection details:" >> "$SSH_OUTPUT_FILE"
    echo "Hostname: ${aws_hostname}" >> "$SSH_OUTPUT_FILE"
    echo "User: ${aws_user}" >> "$SSH_OUTPUT_FILE"
    echo "Private key path: /home/<USER>/.ssh/aws_private_key" >> "$SSH_OUTPUT_FILE"
fi

echo "=== SSH Connection Test Completed at $(date) ===" >> "$SSH_OUTPUT_FILE"

# Display the SSH test output in the startup log
log_pipeline_message "=== SSH Test Output ==="
cat "$SSH_OUTPUT_FILE"
log_pipeline_message "=== End SSH Test Output ==="

EOF

# Clone GitHub repository if provided
if [ -n "${github_repo}" ] && [ -n "${github_token}" ]; then
    log_message "Cloning GitHub repository..."
    sudo -u pipeline bash << 'EOF'
    cd /home/<USER>

    # Configure git with token authentication
    REPO_URL="${github_repo}"
    if [[ "$REPO_URL" == https://github.com/* ]]; then
        # Insert token into HTTPS URL
        REPO_WITH_TOKEN=$(echo "$REPO_URL" | sed "s|https://github.com/|https://${github_token}@github.com/|")
        git clone "$REPO_WITH_TOKEN" pipeline-repo
    else
        git clone "$REPO_URL" pipeline-repo
    fi

    cd pipeline-repo
    git checkout main || git checkout master

    echo "Repository cloned successfully to /home/<USER>/pipeline-repo"
    echo "Repository cloned successfully to /home/<USER>/pipeline-repo" | tee -a /dev/console
EOF
    log_message "GitHub repository cloned successfully"
else
    log_message "No GitHub repository configured, skipping clone"
fi

# Install Python dependencies if requirements.txt exists
if [ -f "/home/<USER>/pipeline-repo/requirements.txt" ]; then
    log_message "Installing Python dependencies..."
    sudo -u pipeline bash << 'EOF'
    cd /home/<USER>/pipeline-repo
    python3 -m venv venv
    source venv/bin/activate
    pip install --upgrade pip
    pip install -r requirements.txt
    echo "Python dependencies installed successfully" | tee -a /dev/console
EOF
    log_message "Python dependencies installed successfully"
else
    log_message "No requirements.txt found, skipping Python dependencies installation"
fi

# Database configuration was already handled earlier in the script

# Create a summary file with important information
log_message "Creating VM setup summary..."
sudo -u pipeline bash << 'EOF'
cat > /home/<USER>/vm_setup_summary.txt << 'SUMMARY_EOF'
=== Data Pipeline VM Setup Summary ===
Setup completed at: $(date)

SSH Configuration:
- Private key: /home/<USER>/.ssh/aws_private_key
- Public key: /home/<USER>/.ssh/aws_public_key
- SSH config: /home/<USER>/.ssh/config
- SSH alias: trips (connects to ${aws_hostname} as ${aws_user})

Test Results:
- SSH test output: /home/<USER>/ssh_test_output.log

Database Configuration:
- MySQL/MariaDB: Available if installed
- Root password: pipeline123 (if configured)
- Pipeline database: pipeline_data (if created)
- Pipeline user: pipeline (password: pipeline123, if created)

Repository:
- Location: /home/<USER>/pipeline-repo (if cloned)
- Python virtual environment: /home/<USER>/pipeline-repo/venv (if created)

Log Files:
- Startup log: /var/log/pipeline-startup.log
- SSH test log: /home/<USER>/ssh_test_output.log

Commands to test SSH:
- ssh trips
- ssh ${aws_user}@${aws_hostname}

Next Steps:
1. Check SSH connectivity: ssh trips
2. Run data pipeline scripts
3. Monitor logs in /var/log/pipeline-startup.log
SUMMARY_EOF
EOF

# Send logs to Google Cloud Logging
if command -v gcloud &> /dev/null; then
    log_message "Sending startup logs to Google Cloud Logging..."
    gcloud logging write pipeline-vm-startup "VM startup completed successfully" --severity=INFO

    # Send SSH test results to logging
    if [ -f "/home/<USER>/ssh_test_output.log" ]; then
        gcloud logging write pipeline-vm-ssh-test "$(cat /home/<USER>/ssh_test_output.log)" --severity=INFO
    fi
    log_message "Logs sent to Google Cloud Logging successfully"
else
    log_message "Google Cloud SDK not available, skipping cloud logging"
fi

# Final completion message - this is what GitHub Actions is waiting for
log_message "=== Data Pipeline VM Startup Script Completed Successfully at $(date) ==="
log_message "Check /home/<USER>/vm_setup_summary.txt for setup details"
log_message "Check /home/<USER>/ssh_test_output.log for SSH test results"

# Ensure the completion message is visible in serial console output
echo "=== Data Pipeline VM Startup Script Completed Successfully at $(date) ===" | tee -a /dev/console
echo "VM is ready for pipeline execution" | tee -a /dev/console
