# VM Startup Issues - Fixes and Improvements

## Issues Identified

Based on the error logs provided, the following issues were causing VM startup timeouts and failures:

1. **Missing Completion Signal**: The startup script wasn't properly logging the completion message to the serial console that GitHub Actions was waiting for
2. **Slow Package Installation**: Package updates and installations were taking too long, causing timeouts
3. **Lack of Error Handling**: No retry logic or error recovery mechanisms for transient failures
4. **Insufficient Monitoring**: GitHub Actions timeout was too short and lacked detailed progress monitoring
5. **Suboptimal VM Configuration**: Using standard disk instead of SSD, no boot optimizations

## Fixes Implemented

### 1. Fixed Startup Script Completion Signal ✅

**Problem**: GitHub Actions was waiting for "Data Pipeline VM Startup Script Completed Successfully" message but it wasn't being logged to the serial console properly.

**Solution**:
- Added `log_message()` function that logs to both file and serial console (`/dev/console`)
- Added `logger` command to send messages to system log
- Ensured completion message is explicitly written to serial console with `tee -a /dev/console`
- Added error handling with `handle_error()` function and trap for failures

### 2. Optimized Package Installation Process ✅

**Problem**: Package installation was slow and causing timeouts.

**Solution**:
- Added `DEBIAN_FRONTEND=noninteractive` to prevent interactive prompts
- Used `-qq` flag for quiet installation to reduce output
- Combined package installations into fewer commands
- Added progress indicators for better monitoring
- Disabled unnecessary services (snapd) to speed up boot

### 3. Added Error Handling and Recovery ✅

**Problem**: No retry logic for transient failures during package installation.

**Solution**:
- Added `retry_command()` function with exponential backoff
- Implemented retry logic for critical operations:
  - System package updates (3 retries, 5s initial delay)
  - Package installations (3 retries, 10s initial delay)
  - Database client installation (3 retries, 5s initial delay)
- Added proper error trapping with `trap 'handle_error ${LINENO} $?' ERR`

### 4. Improved GitHub Actions Timeout and Monitoring ✅

**Problem**: 15-minute timeout was too short, and monitoring was insufficient.

**Solution**:
- Increased timeout from 15 to 20 minutes (90 → 120 attempts)
- Added detailed progress indicators showing current startup phase
- Improved error detection to catch failures early
- Added diagnostic information on timeout (VM status, operation logs)
- Enhanced log output with emojis and better formatting
- Added failure detection for early exit on errors

### 5. VM Resource Optimization ✅

**Problem**: VM was using standard disk and had no boot optimizations.

**Solution**:
- Changed disk type from `pd-standard` to `pd-ssd` for faster I/O
- Added VM scheduling optimizations
- Enabled serial port for better logging
- Added system optimizations in startup script
- Disabled unnecessary services to reduce boot time

## Key Changes Made

### Startup Script (`infrastructure/terraform/startup-script.sh`)
```bash
# New logging function
log_message() {
    local message="$1"
    echo "$message"
    echo "$message" | tee -a /dev/console
    logger -t "pipeline-startup" "$message"
}

# New retry function
retry_command() {
    local max_attempts=$1
    local delay=$2
    local command="${@:3}"
    # ... retry logic with exponential backoff
}

# Error handling
trap 'handle_error ${LINENO} $?' ERR
```

### GitHub Actions Workflow (`.github/workflows/run-pipeline.yml`)
- Increased timeout: 15 → 20 minutes
- Added progress indicators and detailed logging
- Improved error detection and reporting
- Added diagnostic information on failures

### Terraform Configuration (`infrastructure/terraform/main.tf`)
- Changed disk type: `pd-standard` → `pd-ssd`
- Added VM scheduling optimizations
- Enabled serial port logging

## Expected Results

With these fixes, the VM startup should:

1. **Complete within timeout**: Optimizations should reduce startup time to under 15 minutes
2. **Provide clear progress**: GitHub Actions will show detailed progress indicators
3. **Handle failures gracefully**: Retry logic will handle transient network/package issues
4. **Fail fast on real errors**: Early error detection will prevent long waits on actual failures
5. **Be more reliable**: SSD disk and optimizations will improve overall performance

## Testing Recommendations

To validate these fixes:

1. **Run the pipeline**: Use the GitHub Actions "Run workflow" button
2. **Monitor progress**: Check that progress indicators show up correctly
3. **Verify completion**: Ensure the completion message appears in logs
4. **Test error handling**: Temporarily break something to test retry logic
5. **Check performance**: Startup should complete in 10-15 minutes instead of timing out

## Rollback Plan

If issues persist:
1. Revert disk type back to `pd-standard` if SSD causes issues
2. Reduce timeout back to 15 minutes if 20 minutes is too long
3. Remove retry logic if it causes unexpected behavior
4. Disable service optimizations if they break functionality

The changes are backward compatible and can be reverted individually if needed.
